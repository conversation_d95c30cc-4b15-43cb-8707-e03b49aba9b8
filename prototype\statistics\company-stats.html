<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业统计分析 - 幕墙信息化平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Microsoft YaHei', sans-serif; }
        .navbar { background: linear-gradient(45deg, #667eea, #764ba2); box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .sidebar { background: white; min-height: calc(100vh - 76px); box-shadow: 2px 0 10px rgba(0,0,0,0.1); padding: 0; }
        .sidebar-menu { list-style: none; padding: 1rem 0; margin: 0; }
        .sidebar-menu li { margin-bottom: 0.5rem; }
        .sidebar-menu a { display: block; padding: 0.8rem 1.5rem; color: #333; text-decoration: none; transition: all 0.3s ease; border-left: 3px solid transparent; }
        .sidebar-menu a:hover, .sidebar-menu a.active { background: rgba(102, 126, 234, 0.1); border-left-color: #667eea; color: #667eea; }
        .sidebar-menu i { width: 20px; margin-right: 10px; }
        .main-content { padding: 2rem; }
        .page-header { background: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
        .stats-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s ease; margin-bottom: 2rem; }
        .stats-card:hover { transform: translateY(-5px); }
        .stats-icon { width: 60px; height: 60px; border-radius: 15px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; margin: 0 auto 1rem; }
        .stats-number { font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; }
        .stats-label { color: #666; font-size: 0.9rem; }
        .chart-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .chart-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 1rem; color: #333; }
        .filter-card { background: white; border-radius: 15px; padding: 1.5rem; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 2rem; }
        .btn-primary { background: linear-gradient(45deg, #667eea, #764ba2); border: none; border-radius: 8px; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .ranking-item { display: flex; justify-content: space-between; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 0.5rem; }
        .ranking-number { width: 30px; height: 30px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; }
        .ranking-number.top3 { background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; }
        .trend-indicator { font-size: 0.8rem; margin-left: 0.5rem; }
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.html"><i class="fas fa-building"></i> 幕墙信息化平台</a>
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> 管理员
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="../login.html"><i class="fas fa-sign-out-alt"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-lg-2 p-0">
                <div class="sidebar">
                    <ul class="sidebar-menu">
                        <li><a href="../dashboard.html"><i class="fas fa-tachometer-alt"></i> 系统首页</a></li>
                        <li><a href="../company/list.html"><i class="fas fa-industry"></i> 企业管理</a></li>
                        <li><a href="../project/list.html"><i class="fas fa-project-diagram"></i> 项目管理</a></li>
                        <li><a href="../curtain-wall/list.html"><i class="fas fa-building-columns"></i> 既有幕墙</a></li>
                        <li><a href="overview.html" class="active"><i class="fas fa-chart-bar"></i> 统计分析</a></li>
                        <li><a href="../system/users.html"><i class="fas fa-users"></i> 系统管理</a></li>
                    </ul>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- 页面标题 -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-2">
                                        <li class="breadcrumb-item"><a href="overview.html">统计分析</a></li>
                                        <li class="breadcrumb-item active">企业统计</li>
                                    </ol>
                                </nav>
                                <h2><i class="fas fa-industry"></i> 企业统计分析</h2>
                                <p class="text-muted mb-0">企业注册、分布、规模等多维度统计分析</p>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="window.location.href='project-stats.html'">
                                    <i class="fas fa-project-diagram"></i> 项目统计
                                </button>
                                <button class="btn btn-primary" onclick="exportReport()">
                                    <i class="fas fa-download"></i> 导出报表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选条件 -->
                    <div class="filter-card">
                        <h5><i class="fas fa-filter"></i> 筛选条件</h5>
                        <form class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">企业类型</label>
                                <select class="form-select">
                                    <option value="">全部类型</option>
                                    <option value="1">幕墙材料企业</option>
                                    <option value="2">幕墙施工企业</option>
                                    <option value="3">既有幕墙维修企业</option>
                                    <option value="4">既有幕墙检查服务企业</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">注册地区</label>
                                <select class="form-select">
                                    <option value="">全部地区</option>
                                    <option value="pudong">浦东新区</option>
                                    <option value="huangpu">黄浦区</option>
                                    <option value="jingan">静安区</option>
                                    <option value="xuhui">徐汇区</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">注册时间</label>
                                <select class="form-select">
                                    <option value="">全部时间</option>
                                    <option value="2024">2024年</option>
                                    <option value="2023">2023年</option>
                                    <option value="2022">2022年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> 筛选
                                    </button>
                                    <button type="reset" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 核心指标卡片 -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="stats-number text-primary">156</div>
                                <div class="stats-label">注册企业总数</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 8.5%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #28a745, #20c997);">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stats-number text-success">142</div>
                                <div class="stats-label">活跃企业数量</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较上月增长 5.2%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #ffc107, #fd7e14);">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stats-number text-warning">2,847</div>
                                <div class="stats-label">从业人员总数</div>
                                <small class="text-info"><i class="fas fa-arrow-up"></i> 较上月增长 3.1%</small>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="stats-card">
                                <div class="stats-icon" style="background: linear-gradient(45deg, #dc3545, #e83e8c);">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stats-number text-danger">45.8亿</div>
                                <div class="stats-label">年产值总额</div>
                                <small class="text-success"><i class="fas fa-arrow-up"></i> 较去年增长 12.3%</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 企业类型分布 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-pie"></i> 企业类型分布
                                </h5>
                                <canvas id="companyTypeChart" height="300"></canvas>
                            </div>
                        </div>

                        <!-- 企业规模分布 -->
                        <div class="col-lg-6">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-bar"></i> 企业规模分布（按员工数）
                                </h5>
                                <canvas id="companySizeChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 注册趋势 -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-line"></i> 企业注册趋势（近12个月）
                                </h5>
                                <canvas id="registrationTrendChart" height="300"></canvas>
                            </div>
                        </div>

                        <!-- 地区排行榜 -->
                        <div class="col-lg-4">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-trophy"></i> 地区企业数量排行
                                </h5>
                                <div class="ranking-item">
                                    <div class="d-flex align-items-center">
                                        <div class="ranking-number top3">1</div>
                                        <div class="ms-3">
                                            <div class="fw-bold">浦东新区</div>
                                            <small class="text-muted">28家企业</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-primary">17.9%</div>
                                        <span class="trend-indicator trend-up">
                                            <i class="fas fa-arrow-up"></i> 5.2%
                                        </span>
                                    </div>
                                </div>
                                <div class="ranking-item">
                                    <div class="d-flex align-items-center">
                                        <div class="ranking-number top3">2</div>
                                        <div class="ms-3">
                                            <div class="fw-bold">黄浦区</div>
                                            <small class="text-muted">22家企业</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-success">14.1%</div>
                                        <span class="trend-indicator trend-up">
                                            <i class="fas fa-arrow-up"></i> 2.8%
                                        </span>
                                    </div>
                                </div>
                                <div class="ranking-item">
                                    <div class="d-flex align-items-center">
                                        <div class="ranking-number top3">3</div>
                                        <div class="ms-3">
                                            <div class="fw-bold">静安区</div>
                                            <small class="text-muted">18家企业</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-warning">11.5%</div>
                                        <span class="trend-indicator trend-stable">
                                            <i class="fas fa-minus"></i> 0.0%
                                        </span>
                                    </div>
                                </div>
                                <div class="ranking-item">
                                    <div class="d-flex align-items-center">
                                        <div class="ranking-number">4</div>
                                        <div class="ms-3">
                                            <div class="fw-bold">徐汇区</div>
                                            <small class="text-muted">16家企业</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-info">10.3%</div>
                                        <span class="trend-indicator trend-down">
                                            <i class="fas fa-arrow-down"></i> 1.2%
                                        </span>
                                    </div>
                                </div>
                                <div class="ranking-item">
                                    <div class="d-flex align-items-center">
                                        <div class="ranking-number">5</div>
                                        <div class="ms-3">
                                            <div class="fw-bold">长宁区</div>
                                            <small class="text-muted">14家企业</small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-secondary">9.0%</div>
                                        <span class="trend-indicator trend-up">
                                            <i class="fas fa-arrow-up"></i> 1.8%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 年产值分析 -->
                    <div class="row">
                        <div class="col-12">
                            <div class="chart-card">
                                <h5 class="chart-title">
                                    <i class="fas fa-money-bill-wave"></i> 年产值分布分析
                                </h5>
                                <canvas id="revenueDistributionChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 企业类型分布图表
        const companyTypeCtx = document.getElementById('companyTypeChart').getContext('2d');
        new Chart(companyTypeCtx, {
            type: 'doughnut',
            data: {
                labels: ['幕墙材料企业', '幕墙施工企业', '维修企业', '检查服务企业'],
                datasets: [{
                    data: [45, 52, 32, 27],
                    backgroundColor: ['#667eea', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } }
            }
        });

        // 企业规模分布图表
        const companySizeCtx = document.getElementById('companySizeChart').getContext('2d');
        new Chart(companySizeCtx, {
            type: 'bar',
            data: {
                labels: ['1-50人', '51-100人', '101-200人', '201-500人', '500人以上'],
                datasets: [{
                    label: '企业数量',
                    data: [45, 38, 28, 22, 23],
                    backgroundColor: '#667eea'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 注册趋势图表
        const registrationCtx = document.getElementById('registrationTrendChart').getContext('2d');
        new Chart(registrationCtx, {
            type: 'line',
            data: {
                labels: ['2023-03', '2023-04', '2023-05', '2023-06', '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12', '2024-01', '2024-02'],
                datasets: [{
                    label: '新注册企业',
                    data: [8, 12, 15, 18, 22, 25, 28, 32, 35, 38, 42, 45],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 年产值分布图表
        const revenueCtx = document.getElementById('revenueDistributionChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['1000万以下', '1000万-5000万', '5000万-1亿', '1亿-5亿', '5亿以上'],
                datasets: [{
                    label: '企业数量',
                    data: [35, 42, 38, 28, 13],
                    backgroundColor: ['#667eea', '#28a745', '#ffc107', '#fd7e14', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } }
            }
        });

        // 导出报表
        function exportReport() {
            alert('正在生成企业统计报表...');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stats-card, .chart-card, .filter-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
