# 幕墙信息化平台原型

## 原型说明

本目录包含上海市装饰装修行业协会幕墙信息化平台的HTML原型，用于展示系统的主要功能界面和交互流程。

## 原型结构

```
prototype/
├── index.html                 # 原型导航首页
├── login.html                 # 登录页面
├── register.html              # 用户注册页面
├── dashboard.html             # 系统首页/仪表板
├── company/
│   ├── list.html             # 企业列表页面
│   ├── detail.html           # 企业详情页面
│   ├── edit.html             # 企业信息编辑页面
│   └── qualification.html    # 企业资质管理页面
├── project/
│   ├── list.html             # 项目列表页面
│   ├── detail.html           # 项目详情页面
│   ├── edit.html             # 项目信息编辑页面
│   └── progress.html         # 项目进度管理页面
├── curtain-wall/
│   ├── list.html             # 既有幕墙列表页面
│   ├── detail.html           # 既有幕墙详情页面
│   ├── inspection.html       # 检查记录页面
│   └── maintenance.html      # 维修记录页面
├── statistics/
│   ├── overview.html         # 统计概览页面
│   ├── company-stats.html    # 企业统计页面
│   ├── project-stats.html    # 项目统计页面
│   └── reports.html          # 报表生成页面
├── system/
│   ├── users.html            # 用户管理页面
│   ├── roles.html            # 角色管理页面
│   ├── settings.html         # 系统设置页面
│   └── logs.html             # 操作日志页面
├── assets/
│   ├── css/
│   │   ├── bootstrap.min.css # Bootstrap样式
│   │   ├── custom.css        # 自定义样式
│   │   └── icons.css         # 图标样式
│   ├── js/
│   │   ├── bootstrap.min.js  # Bootstrap脚本
│   │   ├── jquery.min.js     # jQuery库
│   │   ├── chart.min.js      # 图表库
│   │   └── custom.js         # 自定义脚本
│   └── images/
│       ├── logo.png          # 系统Logo
│       └── placeholder.jpg   # 占位图片
└── README.md                 # 本说明文件
```

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式设计
- **Bootstrap 5**: 响应式UI框架
- **JavaScript**: 交互功能
- **jQuery**: DOM操作
- **Chart.js**: 数据可视化
- **Font Awesome**: 图标库

## 功能特性

### 1. 响应式设计
- 支持桌面端、平板和移动端访问
- 自适应不同屏幕尺寸
- 优化的移动端用户体验

### 2. 用户界面
- 现代化的扁平设计风格
- 一致的视觉规范
- 直观的导航结构
- 友好的用户交互

### 3. 功能模块
- **用户管理**: 登录、注册、权限控制
- **企业管理**: 企业信息、资质、人员、业绩
- **项目管理**: 项目信息、进度、质量管理
- **既有幕墙管理**: 幕墙信息、检查、维修记录
- **统计分析**: 数据统计、图表展示、报表生成
- **系统管理**: 用户管理、角色权限、系统设置

### 4. 交互功能
- 表单验证
- 数据筛选和搜索
- 分页显示
- 模态对话框
- 文件上传
- 图表交互

## 使用说明

### 1. 本地预览
1. 下载或克隆项目到本地
2. 使用Web服务器（如Apache、Nginx或Live Server）运行
3. 访问 `index.html` 查看原型导航页面

### 2. 在线预览
如果部署到Web服务器，可以直接通过浏览器访问原型页面。

### 3. 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 页面说明

### 登录页面 (login.html)
- 用户名/密码登录
- 验证码验证
- 记住密码功能
- 忘记密码链接

### 注册页面 (register.html)
- 多步骤注册流程
- 企业类型选择
- 信息验证
- 协议确认

### 系统首页 (dashboard.html)
- 数据概览
- 快捷操作
- 通知消息
- 统计图表

### 企业管理模块
- **企业列表**: 分页显示、搜索筛选、状态管理
- **企业详情**: 完整信息展示、相关记录
- **信息编辑**: 表单编辑、文件上传、数据验证
- **资质管理**: 资质列表、添加编辑、到期提醒

### 项目管理模块
- **项目列表**: 项目概览、状态筛选、快速操作
- **项目详情**: 详细信息、进度展示、质量记录
- **信息编辑**: 项目信息维护、文档管理
- **进度管理**: 进度节点、时间轴、完成率

### 既有幕墙管理模块
- **幕墙列表**: 建筑信息、安全等级、操作入口
- **幕墙详情**: 技术参数、历史记录、相关文档
- **检查记录**: 检查计划、结果记录、问题跟踪
- **维修记录**: 维修申请、施工记录、验收确认

### 统计分析模块
- **统计概览**: 关键指标、趋势图表、对比分析
- **企业统计**: 企业分布、类型统计、地区分析
- **项目统计**: 项目数量、投资规模、完成情况
- **报表生成**: 自定义报表、导出功能、定时生成

### 系统管理模块
- **用户管理**: 用户列表、权限分配、状态管理
- **角色管理**: 角色定义、权限配置、成员管理
- **系统设置**: 参数配置、字典管理、系统信息
- **操作日志**: 日志查询、操作记录、安全审计

## 数据说明

原型中使用的数据均为示例数据，包括：
- 企业信息示例
- 项目数据示例
- 统计数据示例
- 用户信息示例

实际开发时需要替换为真实的API接口调用。

## 自定义说明

### 样式自定义
可以通过修改 `assets/css/custom.css` 文件来自定义样式：
- 主题颜色
- 字体设置
- 布局调整
- 组件样式

### 功能扩展
可以通过修改 `assets/js/custom.js` 文件来扩展功能：
- 表单验证逻辑
- 数据处理函数
- 交互效果
- API接口调用

## 注意事项

1. 原型仅用于演示界面和交互流程，不包含后端逻辑
2. 部分功能需要JavaScript支持，请确保浏览器启用JavaScript
3. 文件上传等功能在原型中仅为界面展示，无实际上传功能
4. 图表数据为静态数据，实际使用时需要连接真实数据源

## 反馈建议

如有任何问题或建议，请联系开发团队：
- 邮箱: <EMAIL>
- 电话: 021-12345678
